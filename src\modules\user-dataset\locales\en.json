{"user-dataset": {"title": "Dataset & Model Management", "description": "Manage datasets and AI models for users", "dataFineTune": {"title": "Dataset Fine-tune", "description": "Manage datasets for training and fine-tuning AI models."}, "createDataset": {"openai": {"title": "Create OpenAI Dataset", "description": "Create dataset for OpenAI fine-tuning"}, "google": {"title": "Create Google Dataset", "description": "Create dataset for Google AI fine-tuning", "placeholder": "Enter message for Google AI..."}, "formGoogle": {"name": "Name", "description": "Description", "placeholderName": "Enter dataset name for Google...", "placeholderDescription": "Enter dataset description Google..."}, "formOpenAI": {"name": "Name", "description": "Description", "placeholderName": "Enter dataset name for OpenAI...", "placeholderDescription": "Enter dataset description OPenAI..."}, "action": {"cancel": "Cancel", "createDataset": "createDataset"}}, "menu": {"overview": "Overview", "dataFineTune": "Dataset Fine-tune", "createOpenAI": "Create OpenAI Dataset", "createGoogle": "Create Google Dataset"}, "createFineTuneModel": {"title": "Create Fine-tune Model", "form": {"modelName": "Model Name", "description": "Description", "dataset": "Dataset", "baseModel": "Base Model", "suffix": "Suffix", "modelSource": "Base Model Source", "userKeyLLM": "Select User Key LLM", "hyperparameters": "Hyperparameters Configuration", "epochs": "Epochs", "batchSize": "<PERSON><PERSON> Si<PERSON>", "learningRate": "Learning Rate"}, "placeholders": {"modelName": "Enter model name", "description": "Enter model description", "suffix": "Enter model suffix", "selectDataset": "-- Select dataset for fine-tuning --", "selectSystemModel": "-- Select system model --", "selectUserModel": "-- Select user model --", "selectUserKey": "-- Select User Key --", "selectKeyFirst": "Please select LLM key first"}, "buttons": {"systemModels": "System Models", "userModels": "User Models (Keys)", "auto": "Auto", "custom": "Custom", "cancel": "Cancel", "create": "Create Model"}, "loading": {"datasets": "Loading datasets...", "systemModels": "Loading system models...", "userKeys": "Loading keys list...", "userModels": "Loading models from key...", "userKeysLoading": "Loading User Keys list..."}, "messages": {"noDatasets": "No datasets available", "noUserKeys": "No User Keys available", "noUserKeysAvailable": "No User Key LLM available", "noModelsAvailable": "No models available", "noModelsForKey": "No models available for this key", "selectKeyFirst": "Select User Key LLM to view available models", "loadingModelsFromKey": "Loading models from selected key...", "modelsFound": "Found {{count}} models from this key", "loadingError": "Error loading models: {{error}}"}}, "apiIntegration": {"title": "API Integration", "tabs": {"systemModels": "System Models", "userModels": "User Models", "fineTuneModels": "Fine-Tune Models"}, "loading": {"loadingData": "Loading data..."}, "messages": {"noUserKeys": "No User Keys available", "noModelsForKey": "No Models available for selected Key", "noSystemModels": "No System Models available"}, "form": {"selectUserKey": "Select User Key to view Models"}, "placeholders": {"selectUserKey": "-- Select User Key --"}}, "providerSelection": {"createDatasetUsingOpenAI": "Create Dataset using OpenAI", "createDatasetUsingGoogle": "Create Dataset using Google"}, "chatLayout": {"training": {"title": "Training Data Chat", "description": "Import JSONL file or create new conversation for training data", "defaultConversationTitle": "Training Conversation", "defaultChatTitle": "Training Chat", "placeholder": "Enter message for Training Data...", "messages": {"importSuccess": "✅ Successfully imported {{count}} conversation(s) from {{type}} file", "importError": "⚠️ No valid conversations were imported. Please check the file format.", "parseError": "Error parsing file: {{error}}. Please check the file format."}}, "validation": {"title": "Validation Data Chat", "description": "Import JSONL file or create new conversation for validation data", "defaultConversationTitle": "Validation Conversation", "defaultChatTitle": "Validation Chat", "placeholder": "Enter message for Validation Data..."}}, "conversationSidebar": {"title": "Conversations", "buttons": {"closeSidebar": "Close sidebar", "openSidebar": "Open sidebar", "importJsonl": "Import JSONL", "newChat": "New Chat", "deleteConversation": "Delete conversation"}, "messages": {"noConversations": "No conversations yet", "importToStart": "Import JSONL file to get started"}}, "chatPanelWithRoleLogic": {"startConversation": "Start Conversation", "startConversationDescription": "Add messages to create a training dataset"}}}