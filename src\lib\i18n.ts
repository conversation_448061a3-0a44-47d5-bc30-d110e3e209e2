import { initReactI18next } from 'react-i18next';
import i18n from 'i18next';

import enTranslation from '../locales/en.json';
import viTranslation from '../locales/vi.json';
import zhTranslation from '../locales/zh.json';

// Import module translations
import businessResources from '../modules/business/locales';
import authResources from '../modules/auth/locales';
import adminAuthResources from '../modules/admin/auth/locales';
import adminBusinessResources from '../modules/admin/business/locales';
import profileResources from '../modules/profile/locales';
import componentsResources from '../modules/components/locales';
import marketingResources from '../modules/marketing/locales';
import integrationResources from '../modules/integration/locales';
import { subscriptionResources } from '../modules/subscription/locales';
import { subscriptionResources as adminSubscriptionResources } from '../modules/admin/subscription/locales';
import marketplaceResources from '../modules/marketplace/locales';
import rpointResources from '../modules/rpoint/locales';
import aiAgentsResources from '../modules/ai-agents/locales';
import employeeResources from '../modules/admin/employee/locales';
import { rpointAdminResources as rpointAdminResources } from '@/modules/admin/r-point/locales';
import affiliateResources from '@/modules/admin/affiliate/locales';
import userResources from '@/modules/admin/user/locales';
import dataResources from '@/modules/admin/data/locales';
import moduleDataResources from '@/modules/data/locales';
import marketplaceAdminResources from '@/modules/admin/marketplace/locales';
import marketingAdminResources from '@/modules/admin/marketing/locales';
import blogResources from '@/modules/blog/locales';
import agentResources from '@/modules/admin/agent/locales';

import settingsResources from '@/modules/settings/locales';
import adminIntegrationResources from '@/modules/admin/integration/locales';
import contractResources from '@/modules/contract/locales';
import contractAffiliateResources from '@/modules/contract-affiliate/locales';
import { userAffiliateResources } from '@/modules/user/affiliate/locales';
import userDatasetResources from '@/modules/user-dataset/locales';
import adminDatasetResources from '@/modules/admin/dataset/locales';
import calendarResources from '@/modules/calendar/locales';
import adminEnTranslation from './i18n/locales/en/admin.json';
import adminViTranslation from './i18n/locales/vi/admin.json';
import adminZhTranslation from './i18n/locales/zh/admin.json';
import workflowsEnTranslation from './i18n/locales/en/workflows.json';
import workflowsViTranslation from './i18n/locales/vi/workflows.json';
import workflowsZhTranslation from './i18n/locales/zh/workflows.json';
import integrationsEnTranslation from './i18n/locales/en/integrations.json';
import integrationsViTranslation from './i18n/locales/vi/integrations.json';
import integrationsZhTranslation from './i18n/locales/zh/integrations.json';
import paymentEnTranslation from './i18n/locales/en/payment.json';
import paymentViTranslation from './i18n/locales/vi/payment.json';
import paymentZhTranslation from './i18n/locales/zh/payment.json';
import commonEnTranslation from './i18n/locales/en/common.json';
import commonViTranslation from './i18n/locales/vi/common.json';
import commonZhTranslation from './i18n/locales/zh/common.json';
import paginationEnTranslation from './i18n/locales/en/pagination.json';
import paginationViTranslation from './i18n/locales/vi/pagination.json';
import paginationZhTranslation from './i18n/locales/zh/pagination.json';

export const resources = {
  en: {
    translation: enTranslation,
    business: businessResources.en.business,
    auth: authResources.en.auth,
    adminAuth: adminAuthResources.en.adminAuth,
    adminValidation: adminAuthResources.en.validation,
    profile: profileResources.en.profile,
    validation: profileResources.en.validation,
    components: componentsResources.en.components,
    marketing: marketingResources.en.marketing,
    integration: integrationResources.en.integration,
    subscription: subscriptionResources.en.subscription,
    marketplace: marketplaceResources.en.marketplace,
    rpoint: rpointResources.en.rpoint,
    aiAgents: aiAgentsResources.en.aiAgents,
    employee: employeeResources.en.employee,
    rpointAdmin: rpointAdminResources.en.rpoint,
    affiliate: affiliateResources.en.affiliate,
    user: userResources.en.user,
    data: moduleDataResources.en.data,
    blog: blogResources.en,

    settings: settingsResources.en.settings,
    contract: contractResources.en.contract,
    'contract-affiliate': contractAffiliateResources.en['contract-affiliate'],
    userAffiliate: userAffiliateResources.en.userAffiliate,
    'user-dataset': userDatasetResources.en['user-dataset'],
    calendar: calendarResources.en.calendar,
    admin: {
      ...adminEnTranslation,
      ...adminBusinessResources.en.admin,
      data: dataResources.en.data,
      marketplace: marketplaceAdminResources.en.marketplace,
      marketing: marketingAdminResources.en.marketingAdmin,
      agent: agentResources.en.agent,
      integration: adminIntegrationResources.en.adminIntegration,
      subscription: adminSubscriptionResources.en.subscription,
      'admin-dataset': adminDatasetResources.en['admin-dataset'],
    },
    workflows: workflowsEnTranslation,
    integrations: integrationsEnTranslation,
    payment: paymentEnTranslation,
    common: commonEnTranslation,
    pagination: paginationEnTranslation,
  },
  vi: {
    translation: viTranslation,
    business: businessResources.vi.business,
    auth: authResources.vi.auth,
    adminAuth: adminAuthResources.vi.adminAuth,
    adminValidation: adminAuthResources.vi.validation,
    profile: profileResources.vi.profile,
    validation: profileResources.vi.validation,
    components: componentsResources.vi.components,
    marketing: marketingResources.vi.marketing,
    integration: integrationResources.vi.integration,
    subscription: subscriptionResources.vi.subscription,
    marketplace: marketplaceResources.vi.marketplace,
    rpoint: rpointResources.vi.rpoint,
    aiAgents: aiAgentsResources.vi.aiAgents,
    employee: employeeResources.vi.employee,
    rpointAdmin: rpointAdminResources.vi.rpoint,
    affiliate: affiliateResources.vi.affiliate,
    user: userResources.vi.user,
    data: moduleDataResources.vi.data,
    blog: blogResources.vi,

    settings: settingsResources.vi.settings,
    contract: contractResources.vi.contract,
    'contract-affiliate': contractAffiliateResources.vi['contract-affiliate'],
    userAffiliate: userAffiliateResources.vi.userAffiliate,
    'user-dataset': userDatasetResources.vi['user-dataset'],
    calendar: calendarResources.vi.calendar,
    admin: {
      ...adminViTranslation,
      ...adminBusinessResources.vi.admin,
      data: dataResources.vi.data,
      marketplace: marketplaceAdminResources.vi.marketplace,
      marketing: marketingAdminResources.vi.marketingAdmin,
      agent: agentResources.vi.agent,
      integration: adminIntegrationResources.vi.adminIntegration,
      subscription: adminSubscriptionResources.vi.subscription,
      'admin-dataset': adminDatasetResources.vi['admin-dataset'],
    },
    workflows: workflowsViTranslation,
    integrations: integrationsViTranslation,
    payment: paymentViTranslation,
    common: commonViTranslation,
    pagination: paginationViTranslation,
  },
  zh: {
    translation: zhTranslation,
    business: businessResources.zh.business,
    auth: authResources.zh.auth,
    adminAuth: adminAuthResources.zh.adminAuth,
    adminValidation: adminAuthResources.zh.validation,
    profile: profileResources.zh.profile,
    validation: profileResources.zh.validation,
    components: componentsResources.zh.components,
    marketing: marketingResources.zh.marketing,
    integration: integrationResources.zh.integration,
    subscription: subscriptionResources.zh.subscription,
    marketplace: marketplaceResources.zh.marketplace,
    rpoint: rpointResources.zh.rpoint,
    aiAgents: aiAgentsResources.zh.aiAgents,
    employee: employeeResources.zh.employee,
    rpointAdmin: rpointAdminResources.zh.rpoint,
    affiliate: affiliateResources.zh.affiliate,
    user: userResources.zh.user,
    data: moduleDataResources.zh.data,
    blog: blogResources.zh,

    settings: settingsResources.zh.settings,
    contract: contractResources.zh.contract,
    'contract-affiliate': contractAffiliateResources.zh['contract-affiliate'],
    userAffiliate: userAffiliateResources.zh.userAffiliate,
    'user-dataset': userDatasetResources.zh['user-dataset'],
    calendar: calendarResources.zh.calendar,
    admin: {
      ...adminZhTranslation,
      ...adminBusinessResources.zh.admin,
      data: dataResources.zh.data,
      marketplace: marketplaceAdminResources.zh.marketplace,
      marketing: marketingAdminResources.zh.marketingAdmin,
      agent: agentResources.zh.agent,
      integration: adminIntegrationResources.zh.adminIntegration,
      subscription: adminSubscriptionResources.zh.subscription,
      'admin-dataset': adminDatasetResources.zh['admin-dataset'],
    },
    workflows: workflowsZhTranslation,
    integrations: integrationsZhTranslation,
    payment: paymentZhTranslation,
    common: commonZhTranslation,
    pagination: paginationZhTranslation,
  },
};

export const availableLanguages = [
  { code: 'vi', name: 'Tiếng Việt' },
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
];

i18n.use(initReactI18next).init({
  resources,
  lng: 'vi', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false, // React already escapes values
  },
});

export default i18n;
