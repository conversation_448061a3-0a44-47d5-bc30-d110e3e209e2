import React from 'react';
import {
  UserModeBaseResponseDto,
  UserModelsByKeyResponseDto,
} from '../user-mode-base/types/user-mode-base.types';
import { UserModelFineTuneResponseDto } from '../user-mode-fine-tune/types/user-model-fine-tune.types';
import ModelCard from './ModelCard';
import { ResponsiveGrid } from '@/shared/components/common';

// Union type cho tất cả các loại model
type ModelType =
  | UserModeBaseResponseDto
  | UserModelsByKeyResponseDto
  | UserModelFineTuneResponseDto;

interface ModelGridProps {
  models: ModelType[];
  onSelectModel?: (model: ModelType) => void;
  onViewModel?: (model: ModelType) => void;
  loading?: boolean;
}

/**
 * Component hiển thị danh sách Models dạng grid
 * Sử dụng ResponsiveGrid để tự động điều chỉnh số cột dựa trên kích thước màn hình
 *
 * Responsive:
 * - Mobile (<640px): 1 column
 * - Small Tablet (640px-767px): 1-2 columns
 * - Tablet (768px-1023px): 2 columns
 * - Desktop (1024px-1279px): 2-3 columns
 * - Large Desktop (≥1280px): 3-4 columns
 */
const ModelGrid: React.FC<ModelGridProps> = ({ models, onViewModel }) => {
  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {models.map((model, index) => (
        <div key={model.id || index} className="h-full">
          <ModelCard model={model} onClick={() => onViewModel?.(model)} />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default ModelGrid;
